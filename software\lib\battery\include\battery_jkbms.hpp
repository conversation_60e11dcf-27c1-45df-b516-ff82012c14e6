#pragma once
#include <stdint.h>
//--------------------------------------------------------------//
// JK BMS ID
//--------------------------------------------------------------//
enum class JKBMS_DID {
  BATT_ST_1 = 0x02F4, // Battery Status Information 1 (Standard frame, 20ms)
  CELL_VOLT = 0x04F4, // Cell Voltage (Standard frame, 100ms)
  CELL_TEMP = 0x05F4, // Battery Temperature (Standard frame, 500ms)
  ALM_INFO = 0x07F4,  // Warning Information (Standard frame, 100ms)
  BATT_ST_2 =
      0x18F128F4,        // Battery Status Information 2 (Extended frame, 100ms)
  ALL_TEMP = 0x18F228F4, // All Temperatures of the Cell (Extended frame, 500ms)
  BMSERR_INFO = 0x18F328F4,   // BMS Fault Message (Extended frame, 100ms)
  BMS_INFO = 0x18F428F4,      // BMS Information (Extended frame, 500ms)
  BMS_SW_STATUS = 0x18F528F4, // BMS Switch Status (Extended frame, 500ms)
  CELL_VOL = 0x18E028F4,      // Cell Voltage (Extended frame, 1000ms)
  BMSCHG_INFO = 0x1806E5F4,   // BMS Charging Request (Extended frame, 500ms)
  CTRL_INFO =
      0x18F0F428, // Control Information (Extended frame, BMS peripheral)
};

void read_battery_status_1(const uint8_t message[8], float *battery_voltage,
                           float *battery_current, float *soc);
void read_battery_status_2(const uint8_t message[8]);
void read_cell_voltage(const uint8_t message[8]);
void read_cell_temperature(const uint8_t message[8]);
void read_bms_information(const uint8_t message[8]);
void read_switch_status(const uint8_t message);
void read_control_information(const uint8_t message[8]);
void read_charging_request(const uint8_t message[8]);
