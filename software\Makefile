# CREATED BY VIM-PIO
all:
	platformio -f -c vim run -e teensy41

upload:
	platformio -f -c vim run --target upload -e teensy41

clean:
	platformio -f -c vim run --target clean -e teensy41
	platformio -f -c vim run --target clean_microros -e teensy41

program:
	platformio -f -c vim run --target program -e teensy41

uploadfs:
	platformio -f -c vim run --target uploadfs -e teensy41

check:
	platformio -f -c vim check --flags "-DDEBUG cppcheck: --std=c++11" --skip-packages --fail-on-defect=low

test: FORCE
	platformio -f -c vim test -e native -vv

FORCE:
