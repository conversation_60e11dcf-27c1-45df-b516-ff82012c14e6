FROM ros:humble
ARG USERNAME="10xu"
ENV USERNAME=${USERNAME}
ENV TERM=xterm-256color

# Temp fix for key expiry from ROS team (https://github.com/ros2/ros2/issues/1696)
RUN rm -f /etc/apt/sources.list.d/ros2-latest.list && \
    apt update && apt install curl -y && \
    curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu $(. /etc/os-release && echo $UBUNTU_CODENAME) main" | sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null


RUN apt-get update && \
    apt-get install -y --no-install-recommends \
      make python3 python3-dev python3-pip python3-venv git cmake udev sudo snapd nodejs npm unzip && \
    rm -rf /var/lib/apt/lists/*

RUN useradd -m -s /bin/bash ${USERNAME} -p "" \
    && usermod -a -G sudo,dialout,plugdev ${USERNAME} \
    && echo "${USERNAME} ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/${USERNAME} \
    && chmod 0440 /etc/sudoers.d/${USERNAME}


RUN curl -fsSL https://raw.githubusercontent.com/platformio/platformio-core/develop/platformio/assets/system/99-platformio-udev.rules \
    | tee /etc/udev/rules.d/99-platformio-udev.rules

RUN chmod +rw /dev/tty*
USER ${USERNAME}

WORKDIR /home/<USER>

RUN curl -L -o /home/<USER>/nvim.appimage https://github.com/neovim/neovim/releases/download/stable/nvim-linux-x86_64.appimage && \
    chmod u+x /home/<USER>/nvim.appimage && \
    sudo mkdir -p /opt/nvim && \
    sudo mv nvim.appimage /opt/nvim/nvim

ENV PATH="$PATH:/opt/nvim/"

RUN curl -fsSL -o get-platformio.py https://raw.githubusercontent.com/platformio/platformio-core-installer/master/get-platformio.py
RUN python3 get-platformio.py \
    && rm get-platformio.py
ENV PATH="$PATH:/home/<USER>/.platformio/penv/bin"

COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN sudo chmod +x /usr/local/bin/entrypoint.sh

ENTRYPOINT ["/bin/bash", "/usr/local/bin/entrypoint.sh"]

