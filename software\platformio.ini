; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:teensy41]
platform = teensy
board = teensy41
framework = arduino
lib_deps = 
    **************:10xConstruction/template-embedded-systems.git#main
    pierremolinaro/ACAN_T4@^1.1.8
board_microros_distro = humble
board_microros_transport = native_ethernet
upload_protocol = teensy-cli

build_flags =
    -Iinclude
    -Iextra_packages
    ; -DTESTING_MODE
    -O2
    -DNDEBUG
    -DROS_DOMAIN_ID=12

check_tool = cppcheck, clangtidy
check_flags =
  --common-flag
  cppcheck: --enable=performance --inline-suppr
  clangtidy: -fix-errors -format-style=llvm
check_severity = low

[env:native]
platform = native
lib_compat_mode = off
lib_deps =
    ArduinoFake

build_flags =
    -Iinclude

