#include <cstdint>

#include <Arduino.h>

#include "relay_control.hpp"

Relay::Relay(uint8_t pin): m_pin(pin) {
    pinMode(pin, OUTPUT);
}

void Relay::toggle() {
    digitalWrite(m_pin, !digitalRead(m_pin));
}

ActiveHighRelay::ActiveHighRelay(uint8_t pin): Relay::Relay(pin) {
    this->disable();
}

void ActiveHighRelay::enable() {
    digitalWrite(m_pin, HIGH);
}

void ActiveHighRelay::disable() {
    digitalWrite(m_pin, LOW);
}

ActiveLowRelay::ActiveLowRelay(uint8_t pin): Relay::Relay(pin) {
    this->disable();
}

void ActiveLowRelay::enable() {
    digitalWrite(m_pin, LOW);
}

void ActiveLowRelay::disable() {
    digitalWrite(m_pin, HIGH);
}


