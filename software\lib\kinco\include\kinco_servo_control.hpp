#pragma once
#include "config.hpp"
#include "kinco_can_open_object_dictionary.hpp"
#include <ACAN_T4.h>
#include <cfloat>
#include <stdint.h>

// CAN Specific configurations
#define CAN_MSG_DELAY 10.0f

class kincoServoControl {
public:
  // Accepted
  kincoServoControl(
          uint8_t node_id,
          uint16_t heatbeatTime_ms,
          float acc_dec_conversion_factor,
          float pos_vel_conversion_factor,
          float min_value,
          float max_value
  );
  ~kincoServoControl() = default;

  void m_setNodeHeartbeatTime(
          uint8_t nodeID,
          uint16_t heartBeatTime_ms
  );
  void m_emergencyStopWheel();

  virtual void m_initializeMotor(bool invert_direction) = 0;
  virtual void m_enableMotor(bool enable) = 0;
  void m_setProfileAcceleration_RPS(
          float profileAccel_RPS,
          float profileDeaccel_RPS
  );
  void m_setProfileSpeed_RPM(float profileSpeed_RPM);
  bool m_checkWithinLimits(float requested_value);
  void requestEncoderReadings();
  void parseEncoderReading(size_t dataLen, uint8_t *data);

protected:
  const uint16_t m_heartBeatTime_ms;
  const uint8_t m_motorNodeID;
  const float m_acc_dec_conversion_factor;
  const float m_pos_vel_conversion_factor;
  const float m_profilAcceleration_RPS = 100.0f;
  const float m_profilDecceleration_RPS = 100.0f;
  const float m_profileSpeed_RPM = 250.0f;
  uint8_t m_motorDirection = INVERT_DIR_CW;
  const float m_minValue, m_maxValue;

  int32_t m_latestEncoderEnc;

  enum class m_dataType { U8, U16, U32, I8, I16, I32 };
  enum class m_operationType { DOWNLOAD, SDO_READ };

  const CANMessage m_generateMessage(
          m_operationType operationType,
          uint8_t nodeID,
          m_dataType dataType,
          uint16_t index,
          uint8_t subIndex,
          uint32_t data
  );
};

class kincoMotorVelocityControlMode : public kincoServoControl {
public:
  kincoMotorVelocityControlMode(uint8_t node_id,
                                uint16_t heatbeatTime_ms,
                                float acc_dec_conversion_factor,
                                float pos_vel_conversion_factor,
                                float min_speed,
                                float max_speed);
  ~kincoMotorVelocityControlMode() = default;

  void m_initializeMotor(bool invert_direction);
  void m_enableMotor(bool enable);
  void m_setMotorSpeed_RPM(const float travelSpeed);

private:
  const float m_travel_pos_vel_conversion_factor =
      (512.0f * TRAVEL_ENCODER_RESOLUTION * TRAVEL_MOTOR_GEAR_RATIO) / 1875.0;
  const float m_travel_acc_dec_conversion_factor =
      (65536.0f * TRAVEL_ENCODER_RESOLUTION / 1000.0f) / 4000.0f;

  float m_currentSpeed_RPM = 0.0f;
};

class kincoMotorPositionControlMode : public kincoServoControl {
public:
  kincoMotorPositionControlMode(uint8_t node_id,
                                int32_t motor_homing_offset,
                                uint16_t heartbeatTime_ms,
                                float acc_dec_conversion_factor,
                                float pos_vel_conversion_factor,
                                float min_position,
                                float max_position);
  ~kincoMotorPositionControlMode() = default;
  void m_initializeMotor(bool invert_direction);
  void m_enableMotor(bool enable);
  bool m_setMotorAngle_Degrees(const float absoluteSteeringAngle_Degrees);
  void setMotorEncoderCounts(int32_t enc);
  void setProfileSpeedRPM(float profileSpeedRPM);
  int32_t getLatestEnc();

private:
  const float m_steering_pos_vel_conversion_factor =
      512.0f * STEERING_ENCODER_RESOLUTION / 1875.0;
  const float m_steering_acc_dec_conversion_factor =
      (65536.0f * STEERING_ENCODER_RESOLUTION / 1000.0f) / 4000.0f;
  const int32_t m_homingEncoderOffset_Decimal;

  float m_profileSpeed_RPM = 250.0f;
  float m_currentAngle_deg = FLT_MAX;
};
