#include "inverter_meanwell.hpp"
#include "config.hpp"
#include <ACAN_T4.h>
#include <Arduino.h>

void request_meanwell_inverter_information() {

    CANMessage message;
    message.ext = true;  // Extended frame
    message.rtr = false; // Data frame, not remote transmission request
    message.len = MEANWELL_INVERTER_DATA_LENGTH;
    message.id = MEANWELL_REQUEST_FRAME_ID;

    
    message.data[0] = INVERTER_VOLTAGE__CMD_HIGH;
    message.data[1] = INVERTER_VOLTAGE__CMD_LOW;
    
#ifdef TESTING_MODE
    Serial.print("Message ID sent : ");
    Serial.println(message.id, HEX);
    
    Serial.print("Message Data sent : ");
    Serial.print(message.data[0], HEX);
    Serial.print(" ");
    Serial.print(message.data[1], HEX);
    Serial.print(" (");
    Serial.print("Voltage");
    Serial.println(")");
#endif
    ACAN_T4::can2.tryToSend(message);

    delay(CAN_MSG_DELAY);
}

float convert_meanwell_data_to_float(const uint8_t high_byte, const uint8_t low_byte, float scaling_factor) {
    // Swap bytes as per Meanwell protocol (low and high bytes need to be swapped)
    uint16_t raw_value = (low_byte << 8) | high_byte;

    // Convert to decimal and scale using the provided scaling factor
    float result = (float)raw_value * scaling_factor;

    return result;
}


// Calculate battery capacity percentage from voltage
float calculate_battery_capacity_from_voltage(float voltage) {
    // Clamp voltage to valid range
    if (voltage <= MEANWELL_BATTERY_MIN_VOLTAGE) {
        return 0.0f;
    }
    if (voltage >= MEANWELL_BATTERY_MAX_VOLTAGE) {
        return 100.0f;
    }

    // Linear mapping: (voltage - min) / (max - min) * 100
    float capacity = ((voltage - MEANWELL_BATTERY_MIN_VOLTAGE) /(MEANWELL_BATTERY_MAX_VOLTAGE - MEANWELL_BATTERY_MIN_VOLTAGE)) * 100.0f;

    return capacity;
}

void parse_meanwell_inverter_message(const uint32_t can_id, const uint8_t message[8],float msg_actuation_battery_ros[3]) {

    static bool initialized = false;
    if (!initialized) {
        msg_actuation_battery_ros[0] = 0.0f; // Voltage
        msg_actuation_battery_ros[1] = 0.0f; // Current (always 0 for inverter)
        msg_actuation_battery_ros[2] = 0.0f; // Battery capacity (calculated from voltage)
        initialized = true;
    }

#ifdef TESTING_MODE
    Serial.print("Received CAN ID: 0x");
    Serial.print(can_id, HEX);
    Serial.print(" Data: ");
    for (int i = 0; i < 8; i++) {
        Serial.print(message[i], HEX);
        Serial.print(" ");
    }
    Serial.println();
#endif

    // Verify this is a Meanwell response frame
    if (can_id != MEANWELL_RESPONSE_FRAME_ID) {
        Serial.println("Not a Meanwell response frame");
        return;
    }
    
    uint8_t data_high = message[2];
    uint8_t data_low = message[3];

    float converted_value = convert_meanwell_data_to_float(data_high, data_low, INVERTER_VOLTAGE__SCALING_FACTOR);

    msg_actuation_battery_ros[0] = converted_value;
    msg_actuation_battery_ros[1] = 0.0f;
    msg_actuation_battery_ros[2] = calculate_battery_capacity_from_voltage(converted_value);

#ifdef TESTING_MODE
            Serial.print("Meanwell Inverter Voltage: ");
            Serial.print(converted_value);
            Serial.println(" V");

            Serial.print("Meanwell Inverter Current: ");
            Serial.print(msg_actuation_battery_ros[1]);
            Serial.println(" A (default)");

            Serial.print("Meanwell Inverter Battery Capacity: ");
            Serial.print(msg_actuation_battery_ros[2]);
            Serial.println(" % (calculated from voltage)");
#endif
}
