#include "battery.hpp"
#include "Print.h"
#include "battery_dalybms.hpp"
#include "battery_jkbms.hpp"
#include <Arduino.h>
#include <cstdint>

void request_power_information() {
#if POWER_SOURCE == POWER_SOURCE_BATTERY && BATTERY_BMS == DALYBMS
  request_battery_information();
#elif POWER_SOURCE == POWER_SOURCE_INVERTER
  request_meanwell_inverter_information();
#else
#error "Power source configuration not supported"
#endif
}

void parse_battery_message(const uint32_t can_id, const uint8_t message[8],
                           float msg_actuation_battery_ros[3]) {
#if POWER_SOURCE == POWER_SOURCE_BATTERY && BATTERY_BMS == JKBMS
  switch (can_id) {
  case (uint32_t)JKBMS_DID::BATT_ST_1:
    read_battery_status_1(message, &msg_actuation_battery_ros[0],
                          &msg_actuation_battery_ros[1],
                          &msg_actuation_battery_ros[2]);
    break;

  case (uint32_t)JKBMS_DID::CELL_VOLT:
    read_cell_voltage(message);
    break;

  case (uint32_t)JKBMS_DID::CELL_TEMP:
    read_cell_temperature(message);
    break;

  case (uint32_t)JKBMS_DID::BATT_ST_2:
    read_battery_status_2(message);
    break;

  case (uint32_t)JKBMS_DID::BMS_INFO:
    read_bms_information(message);
    break;

  case (uint32_t)JKBMS_DID::BMS_SW_STATUS:
    read_switch_status(message[0]);
    break;

  case (uint32_t)JKBMS_DID::CTRL_INFO:
    read_control_information(message);
    break;

  case (uint32_t)JKBMS_DID::BMSCHG_INFO:
    read_charging_request(message);
    break;

  default:
    Serial.print("Battery Message Type Parsing Not Programmed. [Data ID]: ");
    Serial.println(can_id, HEX);
    Serial.print("HEX Message: ");
    for (int i = 0; i < 8; i++) {
      Serial.print(message[i], HEX);
      Serial.print(" ");
    }
    Serial.println();
    break;
  }

#elif POWER_SOURCE == POWER_SOURCE_BATTERY && BATTERY_BMS == DALYBMS
  const uint8_t bms_address = DALY_BMS_GET_BMS_ADDRESS(can_id);
  const uint8_t data_id = DALY_BMS_GET_DATA_ID(can_id);
  switch (data_id) {
  case (uint8_t)DALY_BMS_DID::SOC_DATA_ID: {
    parse_battery_soc_data(message, bms_address,
                           msg_actuation_battery_ros);
    break;
  }
  case (uint8_t)DALY_BMS_DID::MIN_MAX_VOLTAGE_DATA_ID:
  case (uint8_t)DALY_BMS_DID::MIN_MAX_TEMPERATURE_DATA_ID:
  case (uint8_t)DALY_BMS_DID::CHARGE_DISCHARGE_MOS_STATUS_DATA_ID:
  case (uint8_t)DALY_BMS_DID::STATUS_INFORMATION_1:
  case (uint8_t)DALY_BMS_DID::CELL_VOLTAGE_1_48:
  case (uint8_t)DALY_BMS_DID::CELL_TEMPERATURE_1_16:
  case (uint8_t)DALY_BMS_DID::CELL_BALANCE_STATE_1_48:
  case (uint8_t)DALY_BMS_DID::CELL_BATTERY_FAILURE_STATUS:
    // These data types are received but not processed - this is normal
    break;
  default:
    Serial.print("Battery Message Type Parsing Not Programmed. [Data ID]: ");
    Serial.println(data_id, HEX);
    //    Serial.print("HEX Message: ");
    //    for (int i = 0; i < 8; i++) {
    //      Serial.print(message[i], HEX);
    //      Serial.print(" ");
    //    }
    //    Serial.println();
  }

#elif POWER_SOURCE == POWER_SOURCE_INVERTER
#ifdef TESTING_MODE
  Serial.println("Calling parse_meanwell_inverter_message");
#endif
  parse_meanwell_inverter_message(can_id, message, msg_actuation_battery_ros);

#else
#error "Power source configuration not supported"
#endif
}
